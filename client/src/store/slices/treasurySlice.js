import { createSlice } from '@reduxjs/toolkit';

// Constants for treasury calculations
const TRADING_VALUE = 10; // Base trading value
const MINING_VALUE = 5;   // Base mining value

// Initial state
const initialState = {
  // Treasury stats
  treasuryStats: {
    revenue: 0,
    expenses: 0,
    net: 0,
    miningRevenue: 0,
    tradingRevenue: 0,
    buildingRevenue: 0,
    salaries: 0,
    charges: 0
  },

  // Loading and error states
  loading: false,
  error: null,
  lastUpdated: null
};

/**
 * Calculate treasury revenue based on mining and trading
 * @param {Object} gameState - Current game state
 * @param {Array} jobs - Array of jobs
 * @param {Array} modifierTables - Array of modifier tables
 * @param {Number} moralModifier - Moral modifier value
 * @returns {Object} - Treasury revenue breakdown
 */
export const calculateTreasuryRevenue = (
  gameState,
  jobs,
  modifierTables,
  moralModifier
) => {
  if (!gameState || !jobs || !modifierTables) {
    return {
      miningRevenue: 0,
      tradingRevenue: 0,
      buildingRevenue: 0,
      totalRevenue: 0
    };
  }

  // Calculate mining revenue
  const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };
  const activeMiners = Math.max(0, miners.number - miners.sick);

  // Get mining modifiers
  const miningGeneralTable = modifierTables.find(t => t.id === 4) || { current_value: 0 };
  const miningTechTable = modifierTables.find(t => t.id === 6) || { current_value: 0 };
  const engineeringTable = modifierTables.find(t => t.id === 5) || { current_value: 0 };

  const miningRevenue = activeMiners *
                       MINING_VALUE *
                       (1 + miningGeneralTable.current_value + miningTechTable.current_value + moralModifier) *
                       (1 + engineeringTable.current_value);

  // Calculate trading revenue
  const craftsmen = jobs.find(j => j.name === 'Craftsman') || { number: 0, sick: 0 };
  const activeCraftsmen = Math.max(0, craftsmen.number - craftsmen.sick);

  // Get trading modifier
  const tradingTable = modifierTables.find(t => t.id === 18) || { current_value: 0 };

  const tradingRevenue = activeCraftsmen *
                        Math.max(0, 1 + tradingTable.current_value + moralModifier) *
                        TRADING_VALUE;

  // Building revenue (from gameState if available)
  const buildingRevenue = gameState.building_revenue || 0;

  const totalRevenue = miningRevenue + tradingRevenue + buildingRevenue;

  return {
    miningRevenue: Math.round(miningRevenue * 100) / 100,
    tradingRevenue: Math.round(tradingRevenue * 100) / 100,
    buildingRevenue: Math.round(buildingRevenue * 100) / 100,
    totalRevenue: Math.round(totalRevenue * 100) / 100
  };
};

/**
 * Calculate treasury expenses based on salaries and charges
 * @param {Array} jobs - Array of jobs
 * @param {Array} chargesGeneralModifiers - General charges modifiers
 * @param {Array} chargesGlobalModifiers - Global charges modifiers
 * @returns {Object} - Treasury expenses breakdown
 */
export const calculateTreasuryExpenses = (
  jobs,
  chargesGeneralModifiers,
  chargesGlobalModifiers
) => {
  if (!jobs || !Array.isArray(jobs)) {
    return {
      salaries: 0,
      charges: 0,
      totalExpenses: 0
    };
  }

  // Calculate salaries
  const salaries = jobs.reduce((sum, job) => {
    const jobNumber = job.number || 0;
    const jobFree = job.free || 0;
    const jobSalary = job.salary || 0;
    return sum + (jobNumber - jobFree) * jobSalary;
  }, 0);

  // Calculate charges
  const chargesPerCycle = Array.isArray(chargesGeneralModifiers)
    ? chargesGeneralModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0)
    : 0;

  const chargesGlobalModifier = Array.isArray(chargesGlobalModifiers)
    ? chargesGlobalModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0)
    : 0;

  // Calculate craftsman effect for charges reduction
  const craftsman = jobs.find(j => j.name === 'Craftsman') || { number: 0, sick: 0 };
  const activeCraftsmen = Math.max(0, craftsman.number - craftsman.sick);
  const craftsmanEffect = Math.min(1, 0.03 * activeCraftsmen);

  // Calculate non-salary charges
  const globalModifierDivisor = 1 + (chargesGlobalModifier * -1);
  const craftsmanDivisor = 1 + craftsmanEffect;

  const charges = chargesPerCycle /
                 (globalModifierDivisor === 0 ? 1 : globalModifierDivisor) /
                 (craftsmanDivisor === 0 ? 1 : craftsmanDivisor);

  const totalExpenses = salaries + charges;

  return {
    salaries: Math.round(salaries * 100) / 100,
    charges: Math.round(charges * 100) / 100,
    totalExpenses: Math.round(totalExpenses * 100) / 100
  };
};

// Create the treasury slice
const treasurySlice = createSlice({
  name: 'treasury',
  initialState,
  reducers: {
    // Update treasury stats based on game state
    updateTreasuryStats: (state, action) => {
      const {
        gameState,
        jobs,
        modifierTables,
        moralModifier,
        chargesGeneralModifiers,
        chargesGlobalModifiers
      } = action.payload;

      console.log('Treasury: Updating treasury stats', {
        gameState: !!gameState,
        jobs: jobs?.length,
        modifierTables: modifierTables?.length,
        moralModifier,
        chargesGeneralModifiers: chargesGeneralModifiers?.length,
        chargesGlobalModifiers: chargesGlobalModifiers?.length
      });

      if (!gameState || !jobs || !modifierTables) return;

      // Calculate revenue
      const revenueData = calculateTreasuryRevenue(
        gameState,
        jobs,
        modifierTables,
        moralModifier || 0
      );

      // Calculate expenses
      const expensesData = calculateTreasuryExpenses(
        jobs,
        chargesGeneralModifiers || [],
        chargesGlobalModifiers || []
      );

      // Calculate net
      const net = revenueData.totalRevenue - expensesData.totalExpenses;

      // Update treasury stats
      state.treasuryStats = {
        revenue: revenueData.totalRevenue,
        expenses: expensesData.totalExpenses,
        net: Math.round(net * 100) / 100,
        miningRevenue: revenueData.miningRevenue,
        tradingRevenue: revenueData.tradingRevenue,
        buildingRevenue: revenueData.buildingRevenue,
        salaries: expensesData.salaries,
        charges: expensesData.charges
      };

      console.log('Treasury: Updated treasury stats', state.treasuryStats);

      state.lastUpdated = Date.now();
    },

    // Reset treasury state
    resetTreasuryState: (state) => {
      state.treasuryStats = initialState.treasuryStats;
      state.error = null;
      state.lastUpdated = null;
    },

    // Set loading state
    setTreasuryLoading: (state, action) => {
      state.loading = action.payload;
    },

    // Set error state
    setTreasuryError: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    }
  }
});

// Export actions
export const {
  updateTreasuryStats,
  resetTreasuryState,
  setTreasuryLoading,
  setTreasuryError
} = treasurySlice.actions;

// Selectors
export const selectTreasuryStats = (state) => state.treasury.treasuryStats;
export const selectTreasuryLoading = (state) => state.treasury.loading;
export const selectTreasuryError = (state) => state.treasury.error;
export const selectTreasuryLastUpdated = (state) => state.treasury.lastUpdated;

// Export reducer
export default treasurySlice.reducer;
